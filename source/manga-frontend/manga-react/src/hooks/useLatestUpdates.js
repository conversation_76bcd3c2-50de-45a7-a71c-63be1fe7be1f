import { useState, useEffect } from 'react';
import mangaService from '../services/manga-service';

const useLatestUpdates = () => {
    const [latestMangas, setLatestMangas] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchLatestUpdates = async () => {
            try {
                setLoading(true);
                const data = await mangaService.getMangaSummaries(0, 12, "lastChapterAddedAt,desc");
                if (data) {
                    setLatestMangas(data.content);
                }
            } catch (err) {
                console.error('Lỗi khi lấy manga mới cập nhật:', err);
                setError('Không thể tải manga mới cập nhật');
            } finally {
                setLoading(false);
            }
        };

        fetchLatestUpdates();
    }, []);

    return {
        latestMangas,
        loading,
        error
    };
};

export default useLatestUpdates;
