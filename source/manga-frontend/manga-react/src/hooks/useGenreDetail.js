import { useState, useEffect, useCallback } from 'react';
import mangaService from '../services/manga-service';

const useGenreDetail = (genreName) => {
    const [mangas, setMangas] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [currentPage, setCurrentPage] = useState(0);
    const [totalPages, setTotalPages] = useState(0);
    const [hasMore, setHasMore] = useState(false);

    const fetchMangas = useCallback(async (page = 0, reset = false) => {
        if (!genreName) return;

        try {
            setLoading(true);
            setError(null);
            
            const result = await mangaService.findByGenre(genreName, page, 12);
            
            if (result) {
                if (reset) {
                    setMangas(result.content);
                } else {
                    setMangas(prev => [...prev, ...result.content]);
                }
                setCurrentPage(result.number);
                setTotalPages(result.totalPages);
                setHasMore(!result.last);
            }
        } catch (err) {
            console.error('Lỗi khi lấy manga theo thể loại:', err);
            setError('Không thể tải manga theo thể loại');
        } finally {
            setLoading(false);
        }
    }, [genreName]);

    useEffect(() => {
        if (genreName) {
            fetchMangas(0, true);
        }
    }, [genreName, fetchMangas]);

    const loadMore = useCallback(() => {
        if (hasMore && !loading) {
            fetchMangas(currentPage + 1, false);
        }
    }, [hasMore, loading, currentPage, fetchMangas]);

    return {
        mangas,
        loading,
        error,
        currentPage,
        totalPages,
        hasMore,
        loadMore
    };
};

export default useGenreDetail;
