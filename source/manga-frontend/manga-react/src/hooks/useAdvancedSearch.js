import { useState, useCallback } from 'react';
import mangaService from '../services/manga-service';

const useAdvancedSearch = () => {
    const [searchResults, setSearchResults] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const performSearch = useCallback(async (searchRequest, page = 0, size = 12, sort = 'lastChapterAddedAt,desc') => {
        try {
            setLoading(true);
            setError(null);
            const results = await mangaService.advancedSearch(searchRequest, page, size, sort);
            setSearchResults(results);
        } catch (err) {
            console.error('Lỗi tìm kiếm nâng cao:', err);
            setError('Không thể thực hiện tìm kiếm');
        } finally {
            setLoading(false);
        }
    }, []);

    const clearResults = useCallback(() => {
        setSearchResults(null);
        setError(null);
    }, []);

    return {
        searchResults,
        loading,
        error,
        performSearch,
        clearResults
    };
};

export default useAdvancedSearch;
