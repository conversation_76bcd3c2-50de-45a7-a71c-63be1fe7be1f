import { useState, useEffect, useCallback } from 'react';
import historyService from '../services/history-service';

const useReadingHistory = () => {
    const [history, setHistory] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [currentPage, setCurrentPage] = useState(0);
    const [totalPages, setTotalPages] = useState(0);
    const [hasMore, setHasMore] = useState(false);

    const fetchHistory = useCallback(async (page = 0, reset = false) => {
        try {
            setLoading(true);
            setError(null);
            
            const result = await historyService.getMyReadingHistory(page, 18);
            
            if (result) {
                if (reset) {
                    setHistory(result.content);
                } else {
                    setHistory(prev => [...prev, ...result.content]);
                }
                setCurrentPage(result.number);
                setTotalPages(result.totalPages);
                setHasMore(!result.last);
            }
        } catch (err) {
            console.error('Lỗi khi l<PERSON>y lịch sử đọc:', err);
            setError('<PERSON>hông thể tải lịch sử đọc');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchHistory(0, true);
    }, [fetchHistory]);

    const loadMore = useCallback(() => {
        if (hasMore && !loading) {
            fetchHistory(currentPage + 1, false);
        }
    }, [hasMore, loading, currentPage, fetchHistory]);

    return {
        history,
        loading,
        error,
        currentPage,
        totalPages,
        hasMore,
        loadMore,
        refresh: () => fetchHistory(0, true)
    };
};

export default useReadingHistory;
