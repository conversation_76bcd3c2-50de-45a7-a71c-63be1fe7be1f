import { useState, useEffect } from 'react';
import mangaService from '../services/manga-service';

const usePersonalRecommendations = () => {
    const [recommendations, setRecommendations] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchRecommendations = async () => {
            try {
                setLoading(true);
                const data = await mangaService.getPersonalRecommendations(6);
                if (data) {
                    setRecommendations(data);
                }
            } catch (err) {
                console.error('Lỗi khi lấy gợi ý cá nhân:', err);
                setError('Không thể tải gợi ý cá nhân');
            } finally {
                setLoading(false);
            }
        };

        fetchRecommendations();
    }, []);

    return {
        recommendations,
        loading,
        error
    };
};

export default usePersonalRecommendations;
