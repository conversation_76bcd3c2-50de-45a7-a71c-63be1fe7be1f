import { useState, useEffect, useCallback } from 'react';
import favoriteService from '../services/favorite-service';

const useFavoriteList = () => {
    const [favorites, setFavorites] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [currentPage, setCurrentPage] = useState(0);
    const [totalPages, setTotalPages] = useState(0);
    const [hasMore, setHasMore] = useState(false);

    const fetchFavorites = useCallback(async (page = 0, reset = false) => {
        try {
            setLoading(true);
            setError(null);
            
            const result = await favoriteService.getFavorites(page, 12);
            
            if (result) {
                if (reset) {
                    setFavorites(result.content);
                } else {
                    setFavorites(prev => [...prev, ...result.content]);
                }
                setCurrentPage(result.number);
                setTotalPages(result.totalPages);
                setHasMore(!result.last);
            }
        } catch (err) {
            console.error('Lỗi khi lấy danh sách yêu thích:', err);
            setError('Không thể tải danh sách yêu thích');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchFavorites(0, true);
    }, [fetchFavorites]);

    const loadMore = useCallback(() => {
        if (hasMore && !loading) {
            fetchFavorites(currentPage + 1, false);
        }
    }, [hasMore, loading, currentPage, fetchFavorites]);

    const removeFavorite = useCallback(async (mangaId) => {
        const success = await favoriteService.removeFavorite(mangaId);
        if (success) {
            setFavorites(prev => prev.filter(fav => fav.manga.id !== mangaId));
        }
        return success;
    }, []);

    return {
        favorites,
        loading,
        error,
        currentPage,
        totalPages,
        hasMore,
        loadMore,
        removeFavorite,
        refresh: () => fetchFavorites(0, true)
    };
};

export default useFavoriteList;
