import { useState, useEffect } from 'react';
import mangaService from '../services/manga-service';

const useRecommendedManga = () => {
    const [recommendedMangas, setRecommendedMangas] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchRecommendedMangas = async () => {
            try {
                setLoading(true);
                const data = await mangaService.getPersonalRecommendations(6);
                if (data) {
                    setRecommendedMangas(data);
                }
            } catch (err) {
                console.error('Lỗi khi lấy gợi ý manga:', err);
                setError('Không thể tải gợi ý manga');
            } finally {
                setLoading(false);
            }
        };

        fetchRecommendedMangas();
    }, []);

    return {
        recommendedMangas,
        loading,
        error
    };
};

export default useRecommendedManga;
