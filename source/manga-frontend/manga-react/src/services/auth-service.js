import {toast} from "react-toastify";
import {identityHttpClient} from "./http-client";
import {handleApiError} from "../utils/error-handler";
import {OAuthConfig} from "../configurations/configuration.js";
import {TOKEN_STORAGE, setTokenExpiry} from "../configurations/api-config";
import {logApiCall} from "../utils/api-logger";

class AuthService {

    /**
     * Đăng nhập với username và password
     * @param {string} username Tên đăng nhập
     * @param {string} password Mật khẩu
     * @returns {Promise<Object|false>} Thông tin xác thực hoặc false nếu thất bại
     */
    async login(username, password) {
        logApiCall('login');
        try {
            const request = {username, password};
            const apiResponse = await identityHttpClient.post('/auth/tokens', request);

            if (apiResponse.code !== 200) {
                // <PERSON><PERSON> lý các mã lỗi cụ thể
                if (apiResponse.code === 1007) {
                    toast.error("Tài khoản của bạn đã bị khóa", {position: "top-right"});
                } else {
                    toast.error("Thông tin đăng nhập không chính xác", {position: "top-right"});
                }
                return false;
            }

            if (!apiResponse.result || !apiResponse.result.authenticated) {
                toast.error("Thông tin đăng nhập không chính xác", {position: "top-right"});
                return false;
            }

            // Lưu access token và refresh token
            localStorage.setItem(TOKEN_STORAGE.ACCESS_TOKEN, apiResponse.result.token);
            localStorage.setItem(TOKEN_STORAGE.REFRESH_TOKEN, apiResponse.result.refreshToken);

            // Lưu thời gian hết hạn của token
            if (apiResponse.result.expiresIn) {
                setTokenExpiry(apiResponse.result.expiresIn);
            }

            return apiResponse.result;
        } catch (error) {
            console.error("Lỗi đăng nhập:", error);
            if (error.response?.data?.code === 1007) {
                toast.error("Tài khoản của bạn đã bị khóa", {position: "top-right"});
                return false;
            }
            toast.error("Thông tin đăng nhập không chính xác", {position: "top-right"});
            return false;
        }
    }

    /**
     * Đăng nhập với Google OAuth
     * @param {string} code Code từ Google OAuth
     * @returns {Promise<Object|false>} Thông tin xác thực hoặc false nếu thất bại
     */
    async googleLogin(code) {
        logApiCall('googleLogin');
        try {
            const request = {
                code,
                redirectUri: OAuthConfig.redirectUri,
            };
            const apiResponse = await identityHttpClient.post('/auth/google/tokens', request);

            if (apiResponse.code !== 200) {
                // Xử lý các mã lỗi cụ thể
                if (apiResponse.code === 1007) {
                    toast.error("Tài khoản của bạn đã bị khóa", {position: "top-right"});
                } else {
                    toast.error(apiResponse.message || "Đăng nhập Google thất bại", {position: "top-right"});
                }
                return false;
            }

            if (!apiResponse.result || !apiResponse.result.authenticated) {
                toast.error("Xác thực Google thất bại", {position: "top-right"});
                return false;
            }

            // Lưu access token và refresh token
            localStorage.setItem(TOKEN_STORAGE.ACCESS_TOKEN, apiResponse.result.token);
            localStorage.setItem(TOKEN_STORAGE.REFRESH_TOKEN, apiResponse.result.refreshToken);

            // Lưu thời gian hết hạn của token
            if (apiResponse.result.expiresIn) {
                setTokenExpiry(apiResponse.result.expiresIn);
            }

            return apiResponse.result;
        } catch (error) {
            console.error("Lỗi đăng nhập Google:", error);

            if (error.response?.data?.code === 1007) {
                toast.error("Tài khoản của bạn đã bị khóa", {position: "top-right"});
                return false;
            }

            toast.error("Đăng nhập Google thất bại", {position: "top-right"});
            return false;
        }
    }

    /**
     * Đăng ký tài khoản mới
     * @param {string} username Tên đăng nhập
     * @param {string} password Mật khẩu
     * @param {string} email Email
     * @returns {Promise<Object|false>} Thông tin người dùng hoặc false nếu thất bại
     */
    async register(username, password, email) {
        logApiCall('register');
        try {
            const request = {username, password, email};
            const apiResponse = await identityHttpClient.post('/users', request);

            if (apiResponse.code !== 201) {
                toast.error(apiResponse.message || "Đăng ký thất bại", {position: "top-right"});
                return false;
            }

            toast.success("Đăng ký thành công! Vui lòng đăng nhập.", {position: "top-right"});
            return apiResponse.result;
        } catch (error) {
            console.error("Lỗi đăng ký:", error);
            return false;
        }
    }

    /**
     * Lấy thông tin người dùng hiện tại từ JWT token
     * @returns {Object|false} Thông tin người dùng hoặc false nếu thất bại
     */
    getCurrentUser() {
        logApiCall('getCurrentUser');
        try {
            const token = localStorage.getItem(TOKEN_STORAGE.ACCESS_TOKEN);
            if (!token) {
                return false;
            }

            // Giải mã JWT token (phần payload)
            const base64Url = token.split('.')[1];
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(atob(base64).split('').map(function (c) {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            }).join(''));

            const payload = JSON.parse(jsonPayload);
            console.log('JWT payload:', payload); // Log ra payload để debug

            return {
                userId: payload.sub, // ID người dùng là subject của token
                email: payload.email, // Email được thêm vào claim
                authProvider: payload.authProvider, // Loại tài khoản (LOCAL, GOOGLE, etc.)
                username: payload.username
            };
        } catch (error) {
            console.error("Lỗi giải mã JWT token:", error);
            return false;
        }
    }

    /**
     * Đăng xuất
     * @returns {Promise<boolean>}
     */
    async logout() {
        logApiCall('logout');
        try {
            // Lấy token hiện tại
            const token = localStorage.getItem(TOKEN_STORAGE.ACCESS_TOKEN);

            // Gọi API logout nếu có token
            if (token) {
                try {
                    console.log('AuthService: Gọi API đăng xuất');
                    await identityHttpClient.post('/auth/tokens/revoke', {token});
                    console.log('AuthService: Đăng xuất thành công trên server');
                } catch (apiError) {
                    console.error('AuthService: Lỗi khi gọi API đăng xuất:', apiError);
                    // Tiếp tục xử lý ngay cả khi API thất bại
                }
            }

            // Xóa tất cả các token khỏi localStorage
            localStorage.removeItem(TOKEN_STORAGE.ACCESS_TOKEN);
            localStorage.removeItem(TOKEN_STORAGE.REFRESH_TOKEN);
            localStorage.removeItem(TOKEN_STORAGE.TOKEN_EXPIRY);
            return true;
        } catch (error) {
            console.error("Lỗi đăng xuất:", error);
            // Vẫn xóa token khỏi localStorage ngay cả khi có lỗi
            localStorage.removeItem(TOKEN_STORAGE.ACCESS_TOKEN);
            localStorage.removeItem(TOKEN_STORAGE.REFRESH_TOKEN);
            localStorage.removeItem(TOKEN_STORAGE.TOKEN_EXPIRY);
            return true; // Vẫn trả về true vì người dùng đã đăng xuất khỏi client
        }
    }

    /**
     * Làm mới token sử dụng refresh token
     * @returns {Promise<Object|false>} Thông tin xác thực mới hoặc false nếu thất bại
     */
    async refreshToken() {
        logApiCall('refreshToken');
        try {
            const refreshToken = localStorage.getItem(TOKEN_STORAGE.REFRESH_TOKEN);
            if (!refreshToken) {
                console.error("Không tìm thấy refresh token");
                return false;
            }

            const request = {refreshToken};
            const apiResponse = await identityHttpClient.post('/auth/tokens/refresh', request);

            if (apiResponse.code !== 200) {
                console.error("Làm mới token thất bại:", apiResponse.message);
                return false;
            }

            if (!apiResponse.result || !apiResponse.result.authenticated) {
                console.error("Xác thực thất bại khi làm mới token");
                return false;
            }

            // Lưu access token mới
            localStorage.setItem(TOKEN_STORAGE.ACCESS_TOKEN, apiResponse.result.token);

            // Lưu thời gian hết hạn mới
            if (apiResponse.result.expiresIn) {
                setTokenExpiry(apiResponse.result.expiresIn);
            }

            return apiResponse.result;
        } catch (error) {
            console.error("Lỗi khi làm mới token:", error);
            return false;
        }
    }

    /**
     * Liên kết tài khoản hiện tại với tài khoản Google
     * @param {string} code Code từ Google OAuth
     * @returns {Promise<boolean>} true nếu liên kết thành công, false nếu thất bại
     */
    async linkGoogleAccount(code) {
        logApiCall('linkGoogleAccount');
        try {
            const request = {
                code,
                redirectUri: OAuthConfig.redirectUri,
            };
            const apiResponse = await identityHttpClient.post('/users/accounts/google', request);

            if (apiResponse.code !== 200) {
                toast.error(apiResponse.message || "Liên kết tài khoản Google thất bại", {position: "top-right"});
                return false;
            }

            toast.success("Liên kết tài khoản Google thành công!", {position: "top-right"});
            return true;
        } catch (error) {
            console.error("Lỗi liên kết tài khoản Google:", error);
            toast.error("Đã xảy ra lỗi khi liên kết tài khoản Google", {position: "top-right"});
            return false;
        }
    }
}
